<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!-- 评论标题栏 -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <TextView
            android:id="@+id/text_comment_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="评论"
            android:textColor="@color/text_light"
            android:textSize="18sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/text_comment_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toEndOf="@id/text_comment_title"
            android:layout_marginStart="8dp"
            android:textColor="@color/color_gray_300"
            android:textSize="16sp"
            android:text="(0)" />
    </RelativeLayout>

    <!-- 评论列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view_comment"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="8dp"
        android:clipToPadding="false" />

    <!-- 评论输入区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical"
        android:background="@color/color_black"
        android:alpha="0.2">

        <EditText
            android:id="@+id/edit_text_comment"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:hint="添加评论..."
            android:textColorHint="@color/color_gray_500"
            android:textColor="@color/text_light"
            android:background="@drawable/comment_input_background"
            android:padding="12dp"
            android:maxLines="3" />

        <Button
            android:id="@+id/btn_send_comment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:text="发送"
            android:textColor="@color/text_light"
            android:background="@drawable/button_primary" />
    </LinearLayout>
</LinearLayout>
