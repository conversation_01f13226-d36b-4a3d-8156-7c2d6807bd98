[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-mergeDebugResources-61:\\layout\\item_hot_search.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-main-64:\\layout\\item_hot_search.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-mergeDebugResources-61:\\layout\\dialog_phone_login.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-main-64:\\layout\\dialog_phone_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-mergeDebugResources-61:\\layout\\dialog_playlist.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-main-64:\\layout\\dialog_playlist.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-mergeDebugResources-61:\\layout\\item_comment_header.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-main-64:\\layout\\item_comment_header.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-mergeDebugResources-61:\\layout\\player_controls.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-main-64:\\layout\\player_controls.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-mergeDebugResources-61:\\layout\\activity_login.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-main-64:\\layout\\activity_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-mergeDebugResources-61:\\layout\\fragment_user_profile.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-main-64:\\layout\\fragment_user_profile.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-mergeDebugResources-61:\\layout\\item_playlist_song.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-main-64:\\layout\\item_playlist_song.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-mergeDebugResources-61:\\layout\\item_online_song.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-main-64:\\layout\\item_online_song.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-mergeDebugResources-61:\\layout\\view_album_cover.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-main-64:\\layout\\view_album_cover.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-mergeDebugResources-61:\\layout\\dialog_intelligence.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-main-64:\\layout\\dialog_intelligence.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-mergeDebugResources-61:\\layout\\dialog_heart_mode.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-main-64:\\layout\\dialog_heart_mode.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-mergeDebugResources-61:\\layout\\dialog_qr_login.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-main-64:\\layout\\dialog_qr_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-mergeDebugResources-61:\\layout\\page_player_lyrics.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-main-64:\\layout\\page_player_lyrics.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-mergeDebugResources-61:\\layout\\item_reply.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-main-64:\\layout\\item_reply.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-mergeDebugResources-61:\\layout\\page_player_playlist.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-main-64:\\layout\\page_player_playlist.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-mergeDebugResources-61:\\layout\\item_search_suggest.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-main-64:\\layout\\item_search_suggest.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-mergeDebugResources-61:\\layout\\fragment_intelligence.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-main-64:\\layout\\fragment_intelligence.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-mergeDebugResources-61:\\layout\\fragment_placeholder.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-main-64:\\layout\\fragment_placeholder.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-mergeDebugResources-61:\\layout\\item_playlist.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-main-64:\\layout\\item_playlist.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-mergeDebugResources-61:\\layout\\activity_player.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-main-64:\\layout\\activity_player.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-mergeDebugResources-61:\\layout\\item_comment.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-main-64:\\layout\\item_comment.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-mergeDebugResources-61:\\layout\\item_song.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-main-64:\\layout\\item_song.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-mergeDebugResources-61:\\layout\\page_player_comments.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-main-64:\\layout\\page_player_comments.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-mergeDebugResources-61:\\layout\\fragment_comment.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-main-64:\\layout\\fragment_comment.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-mergeDebugResources-61:\\layout\\dialog_comment.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-main-64:\\layout\\dialog_comment.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-mergeDebugResources-61:\\layout\\fragment_player.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-main-64:\\layout\\fragment_player.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-mergeDebugResources-61:\\layout\\fragment_driving_mode.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-main-64:\\layout\\fragment_driving_mode.xml"}, {"merged": "com.example.aimusicplayer.app-mergeDebugResources-61:/layout/fragment_player.xml", "source": "com.example.aimusicplayer.app-main-64:/layout/fragment_player.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-mergeDebugResources-61:\\layout\\fragment_top_list.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-main-64:\\layout\\fragment_top_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-mergeDebugResources-61:\\layout\\item_top_list.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-main-64:\\layout\\item_top_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-mergeDebugResources-61:\\layout\\activity_splash.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-main-64:\\layout\\activity_splash.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-mergeDebugResources-61:\\layout\\activity_main.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-main-64:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-mergeDebugResources-61:\\layout\\view_lottie_loading.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.aimusicplayer.app-main-64:\\layout\\view_lottie_loading.xml"}]