<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="8dp"
    android:layout_marginVertical="6dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="3dp"
    app:cardBackgroundColor="?colorSurface"
    android:foreground="?attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="16dp"
        android:paddingVertical="16dp">

        <!-- 用户头像 - 增大尺寸，更适合车载场景 -->
        <de.hdodenhof.circleimageview.CircleImageView
            android:id="@+id/image_comment_avatar"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:src="@drawable/default_avatar"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:civ_border_width="2dp"
            app:civ_border_color="#20000000" />

        <!-- 用户名 - 增大字体，更适合车载场景 -->
        <TextView
            android:id="@+id/text_comment_username"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="?textColorPrimary"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@+id/text_comment_time"
            app:layout_constraintStart_toEndOf="@+id/image_comment_avatar"
            app:layout_constraintTop_toTopOf="@+id/image_comment_avatar"
            tools:text="用户名" />

        <!-- 评论时间 -->
        <TextView
            android:id="@+id/text_comment_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="?textColorSecondary"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/text_comment_username"
            tools:text="2小时前" />

        <!-- 评论内容 - 增大字体和行间距，更适合车载场景 -->
        <TextView
            android:id="@+id/text_comment_content"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:textColor="?textColorPrimary"
            android:textSize="18sp"
            android:lineSpacingExtra="8dp"
            android:textIsSelectable="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/text_comment_username"
            app:layout_constraintTop_toBottomOf="@+id/text_comment_username"
            tools:text="这是评论内容，可能会很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长" />

    <!-- 互动区域 - 优化布局，更适合车载场景 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="@+id/text_comment_content"
        app:layout_constraintTop_toBottomOf="@+id/text_comment_content">

        <!-- 点赞区域 - 增大尺寸，更适合车载场景 -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/layout_like"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:clickable="true"
            android:focusable="true"
            app:cardCornerRadius="20dp"
            app:cardElevation="2dp"
            app:rippleColor="@color/colorAccent"
            app:strokeWidth="0dp">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingHorizontal="16dp"
                android:paddingVertical="10dp">

                <ImageView
                    android:id="@+id/image_like"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_like_outline" />

                <TextView
                    android:id="@+id/text_like_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:textColor="?textColorSecondary"
                    android:textSize="16sp"
                    tools:text="123" />
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- 回复区域 - 增大尺寸，更适合车载场景 -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/layout_reply"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:clickable="true"
            android:focusable="true"
            app:cardCornerRadius="20dp"
            app:cardElevation="2dp"
            app:rippleColor="@color/colorAccent"
            app:strokeWidth="0dp">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingHorizontal="16dp"
                android:paddingVertical="10dp">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_comment" />

                <TextView
                    android:id="@+id/text_reply_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:text="回复"
                    android:textColor="?textColorSecondary"
                    android:textSize="16sp" />
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <View
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1" />

        <!-- 更多操作按钮 - 简化界面，更适合车载场景 -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/layout_more"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:clickable="true"
            android:focusable="true"
            app:cardCornerRadius="20dp"
            app:cardElevation="2dp"
            app:rippleColor="@color/colorAccent"
            app:strokeWidth="0dp">

            <ImageView
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:padding="10dp"
                android:src="@drawable/ic_more_vert" />
        </com.google.android.material.card.MaterialCardView>
    </LinearLayout>

    <!-- 回复列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view_replies"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/text_comment_username"
        app:layout_constraintTop_toBottomOf="@+id/layout_like"
        tools:listitem="@layout/item_reply"
        tools:visibility="gone" />
</androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>
