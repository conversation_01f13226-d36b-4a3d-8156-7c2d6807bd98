<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <attr format="color|reference" name="colorBackground"/>
    <attr format="color|reference" name="textColorPrimary"/>
    <attr format="color|reference" name="textColorSecondary"/>
    <color name="accent">@color/theme_accent</color>
    <color name="accent_color">@color/theme_accent</color>
    <color name="accent_dark">@color/theme_accent_dark</color>
    <color name="accent_light">@color/theme_accent_light</color>
    <color name="background">@color/theme_background</color>
    <color name="background_color">@color/theme_background</color>
    <color name="background_dark">#121212</color>
    <color name="background_dark_lighter">#1E1E1E</color>
    <color name="black">@color/color_black</color>
    <color name="blue">@color/color_blue_500</color>
    <color name="button_color">@color/ui_button</color>
    <color name="button_text_color">@color/ui_button_text</color>
    <color name="card_background">#2A2A2A</color>
    <color name="colorAccent">@color/theme_accent</color>
    <color name="colorBackground">@color/theme_background</color>
    <color name="colorPrimary">@color/theme_primary</color>
    <color name="colorPrimaryDark">@color/theme_primary_dark</color>
    <color name="colorTextPrimary">@color/text_primary</color>
    <color name="colorTextSecondary">@color/text_secondary</color>
    <color name="color_background">@color/theme_background</color>
    <color name="color_black">#FF000000</color>
    <color name="color_blue_500">#2196F3</color>
    <color name="color_blue_700">#1976D2</color>
    <color name="color_blue_900">#0D47A1</color>
    <color name="color_gray_100">#F5F5F5</color>
    <color name="color_gray_200">#EEEEEE</color>
    <color name="color_gray_300">#E0E0E0</color>
    <color name="color_gray_400">#BDBDBD</color>
    <color name="color_gray_50">#FAFAFA</color>
    <color name="color_gray_500">#9E9E9E</color>
    <color name="color_gray_600">#757575</color>
    <color name="color_gray_700">#616161</color>
    <color name="color_gray_800">#424242</color>
    <color name="color_gray_900">#212121</color>
    <color name="color_green_500">#4CAF50</color>
    <color name="color_light_blue_500">#03A9F4</color>
    <color name="color_light_blue_700">#0288D1</color>
    <color name="color_pink_500">#E91E63</color>
    <color name="color_pink_700">#C2185B</color>
    <color name="color_purple_200">#FFBB86FC</color>
    <color name="color_purple_500">#FF6200EE</color>
    <color name="color_purple_700">#FF3700B3</color>
    <color name="color_red_500">#F44336</color>
    <color name="color_teal_200">#FF03DAC5</color>
    <color name="color_teal_700">#FF018786</color>
    <color name="color_transparent">#00000000</color>
    <color name="color_white">#FFFFFFFF</color>
    <color name="color_yellow_500">#FFEB3B</color>
    <color name="dark_gray">@color/color_gray_700</color>
    <color name="divider">@color/ui_divider</color>
    <color name="divider_color">@color/ui_divider</color>
    <color name="driving_mode_background">#000000</color>
    <color name="driving_mode_controls">#2196F3</color>
    <color name="driving_mode_text">#FFFFFF</color>
    <color name="error">@color/theme_error</color>
    <color name="gray">@color/color_gray_500</color>
    <color name="green">@color/color_green_500</color>
    <color name="info">@color/theme_info</color>
    <color name="light_gray">@color/color_gray_300</color>
    <color name="list_item_background">#FFFFFF</color>
    <color name="list_item_background_selected">#E3F2FD</color>
    <color name="lyric_background">#212121</color>
    <color name="lyric_background_color">@color/lyric_background</color>
    <color name="lyric_highlight">#2196F3</color>
    <color name="lyric_highlight_color">@color/lyric_highlight</color>
    <color name="lyric_normal">#9E9E9E</color>
    <color name="lyric_normal_color">@color/lyric_normal</color>
    <color name="nav_background">#000000</color>
    <color name="nav_icon_active">#1E88E5</color>
    <color name="nav_icon_inactive">#787878</color>
    <color name="nav_indicator">#1E88E5</color>
    <color name="player_background">#121212</color>
    <color name="player_control_background">#EDEDED</color>
    <color name="player_controls">#FFFFFF</color>
    <color name="player_progress">#2196F3</color>
    <color name="player_progress_background">#4D4D4D</color>
    <color name="primary">@color/theme_primary</color>
    <color name="primary_color">@color/theme_primary</color>
    <color name="primary_dark">@color/theme_primary_dark</color>
    <color name="primary_dark_color">@color/theme_primary_dark</color>
    <color name="primary_light">@color/theme_primary_light</color>
    <color name="purple_200">@color/color_purple_200</color>
    <color name="purple_500">@color/color_purple_500</color>
    <color name="purple_700">@color/color_purple_700</color>
    <color name="red">@color/color_red_500</color>
    <color name="ripple_color">@color/ui_ripple</color>
    <color name="sakura_accent">#FE8BC6</color>
    <color name="sakura_background">#FFF5F8</color>
    <color name="sakura_divider">#FFE4EE</color>
    <color name="sakura_primary">#FFBAD3</color>
    <color name="sakura_primary_dark">#FF9CB6</color>
    <color name="sakura_text_primary">#66304D</color>
    <color name="sakura_text_secondary">#976681</color>
    <color name="search_background">#F8F9FA</color>
    <color name="search_bg_color">@color/search_background</color>
    <color name="search_stroke">#DADCE0</color>
    <color name="search_stroke_color">@color/search_stroke</color>
    <color name="sidebar_background">#000000</color>
    <color name="sidebar_item_background_selected">#303030</color>
    <color name="sidebar_item_icon_normal">#AAAAAA</color>
    <color name="sidebar_item_icon_selected">#2196F3</color>
    <color name="sidebar_item_text">#FFFFFF</color>
    <color name="splash_background">@color/ui_splash_background</color>
    <color name="success">@color/theme_success</color>
    <color name="teal_200">@color/color_teal_200</color>
    <color name="teal_700">@color/color_teal_700</color>
    <color name="text_dark">#000000</color>
    <color name="text_disabled">#9E9E9E</color>
    <color name="text_hint">#BDBDBD</color>
    <color name="text_light">#FFFFFF</color>
    <color name="text_link">#2196F3</color>
    <color name="text_primary">#212121</color>
    <color name="text_primary_color">@color/text_primary</color>
    <color name="text_secondary">#757575</color>
    <color name="text_secondary_color">@color/text_secondary</color>
    <color name="theme_accent">#42A5F5</color>
    <color name="theme_accent_dark">#F50057</color>
    <color name="theme_accent_light">#FF80AB</color>
    <color name="theme_background">#F5F5F5</color>
    <color name="theme_error">#F44336</color>
    <color name="theme_info">#2196F3</color>
    <color name="theme_primary">#1E88E5</color>
    <color name="theme_primary_dark">#0D47A1</color>
    <color name="theme_primary_light">#BBDEFB</color>
    <color name="theme_success">#4CAF50</color>
    <color name="theme_surface">#FFFFFF</color>
    <color name="theme_warning">#FFC107</color>
    <color name="toolbar_background">#1A1A1A</color>
    <color name="transparent">@color/color_transparent</color>
    <color name="ui_button">#2196F3</color>
    <color name="ui_button_text">#FFFFFF</color>
    <color name="ui_divider">#E0E0E0</color>
    <color name="ui_ripple">#80FFFFFF</color>
    <color name="ui_splash_background">#1E3A5F</color>
    <color name="warning">@color/theme_warning</color>
    <color name="white">@color/color_white</color>
    <color name="yellow">@color/color_yellow_500</color>
    <dimen name="corner_radius_large">16dp</dimen>
    <dimen name="corner_radius_medium">8dp</dimen>
    <dimen name="corner_radius_small">4dp</dimen>
    <dimen name="driving_control_size">96dp</dimen>
    <dimen name="driving_seekbar_height">12dp</dimen>
    <dimen name="driving_seekbar_thumb">24dp</dimen>
    <dimen name="driving_text_size_body">24sp</dimen>
    <dimen name="driving_text_size_small">20sp</dimen>
    <dimen name="driving_text_size_title">30sp</dimen>
    <dimen name="height_button">48dp</dimen>
    <dimen name="height_card">180dp</dimen>
    <dimen name="height_divider">1dp</dimen>
    <dimen name="height_list_item">72dp</dimen>
    <dimen name="height_navigation_item">56dp</dimen>
    <dimen name="height_seekbar">24dp</dimen>
    <dimen name="height_toolbar">56dp</dimen>
    <dimen name="image_cover_large">240dp</dimen>
    <dimen name="image_cover_medium">180dp</dimen>
    <dimen name="image_cover_small">120dp</dimen>
    <dimen name="image_cover_xlarge">320dp</dimen>
    <dimen name="image_thumbnail_large">80dp</dimen>
    <dimen name="image_thumbnail_medium">60dp</dimen>
    <dimen name="image_thumbnail_small">40dp</dimen>
    <dimen name="margin_large">24dp</dimen>
    <dimen name="margin_medium">8dp</dimen>
    <dimen name="margin_normal">16dp</dimen>
    <dimen name="margin_small">4dp</dimen>
    <dimen name="margin_tiny">2dp</dimen>
    <dimen name="margin_xlarge">32dp</dimen>
    <dimen name="margin_xxlarge">48dp</dimen>
    <dimen name="padding_large">24dp</dimen>
    <dimen name="padding_medium">8dp</dimen>
    <dimen name="padding_normal">16dp</dimen>
    <dimen name="padding_small">4dp</dimen>
    <dimen name="padding_tiny">2dp</dimen>
    <dimen name="padding_xlarge">32dp</dimen>
    <dimen name="player_control_size_large">72dp</dimen>
    <dimen name="player_control_size_medium">56dp</dimen>
    <dimen name="player_control_size_small">40dp</dimen>
    <dimen name="player_seekbar_height">6dp</dimen>
    <dimen name="player_seekbar_thumb">16dp</dimen>
    <dimen name="spacing_large">24dp</dimen>
    <dimen name="text_size_body">16sp</dimen>
    <dimen name="text_size_caption">14sp</dimen>
    <dimen name="text_size_headline">24sp</dimen>
    <dimen name="text_size_small">12sp</dimen>
    <dimen name="text_size_subtitle">18sp</dimen>
    <dimen name="text_size_title">20sp</dimen>
    <dimen name="width_fab">56dp</dimen>
    <dimen name="width_icon">24dp</dimen>
    <dimen name="width_sidebar">80dp</dimen>
    <dimen name="width_sidebar_selection_indicator">4dp</dimen>
    <integer name="anim_duration_long">500</integer>
    <integer name="anim_duration_medium">300</integer>
    <integer name="anim_duration_short">150</integer>
    <string name="action_settings">Settings</string>
    <string name="album_cover">专辑封面</string>
    <string name="app_logo">轻聆 App Logo</string>
    <string name="app_name">VoxTunes AI</string>
    <string name="app_slogan">您的专属智能车载音乐伴侣</string>
    <string name="back">返回</string>
    <string name="cancel">取消</string>
    <string name="cancel_retry">取消</string>
    <string name="comment">评论</string>
    <string name="comment_empty_hint">评论内容不能为空</string>
    <string name="comment_hint">说点什么吧...</string>
    <string name="comment_title">%1$s 的评论</string>
    <string name="copyright">© 2024 VoxTunes AI</string>
    <string name="error_auth">登录已过期，请重新登录</string>
    <string name="error_client">客户端错误 (%1$d)，请稍后重试</string>
    <string name="error_network">网络错误，请检查网络连接后重试</string>
    <string name="error_no_network">网络不可用，请检查网络连接</string>
    <string name="error_server">服务器错误 (%1$d)，请稍后重试</string>
    <string name="error_timeout">连接超时，请检查网络后重试</string>
    <string name="error_unknown">未知错误，请稍后重试</string>
    <string name="favorite">收藏</string>
    <string name="guest_login">游客登录</string>
    <string name="heart_mode">心动模式</string>
    <string name="intelligence_hint">根据当前歌曲推荐的相似歌曲</string>
    <string name="intelligence_mode">心动模式</string>
    <string name="intelligence_recommendation">推荐歌曲</string>
    <string name="loading">加载中...</string>
    <string name="login">登录</string>
    <string name="login_failed">登录失败</string>
    <string name="login_successful">登录成功</string>
    <string name="network_error">网络错误</string>
    <string name="network_restored">网络已恢复</string>
    <string name="network_unavailable">网络不可用</string>
    <string name="next">下一首</string>
    <string name="no_comments">暂无评论</string>
    <string name="no_intelligence_songs">暂无推荐歌曲</string>
    <string name="no_lyrics">暂无歌词</string>
    <string name="password_hint">请输入密码</string>
    <string name="pause">暂停</string>
    <string name="phone_login">手机号登录</string>
    <string name="phone_number_hint">请输入手机号</string>
    <string name="play">播放</string>
    <string name="play_mode_loop">列表循环</string>
    <string name="play_mode_shuffle">随机播放</string>
    <string name="play_mode_single">单曲循环</string>
    <string name="playlist">播放列表</string>
    <string name="previous">上一首</string>
    <string name="qr_code_expired">二维码已过期，请点击刷新</string>
    <string name="qr_code_login">扫码登录</string>
    <string name="qr_code_scanned">扫描成功，请在手机上确认登录</string>
    <string name="recommend">推荐新歌</string>
    <string name="refresh_qr_code">刷新二维码</string>
    <string name="retry">重试</string>
    <string name="scan_qr_code_tip">请使用网易云音乐APP扫描二维码登录</string>
    <string name="search">搜索</string>
    <string name="search_hint">输入歌曲名或歌手名</string>
    <string name="send">发送</string>
    <string name="setting_auto_play">自动播放</string>
    <string name="setting_auto_voice_in_driving">驾驶模式自动语音</string>
    <string name="setting_night_mode">夜间模式</string>
    <string name="toplist">音乐排行榜</string>
    <string name="unfavorite">取消收藏</string>
    <string name="user_avatar">用户头像</string>
    <string name="version_info">轻聆音乐 v1.0</string>
    <string name="vip">VIP</string>
    <string name="welcome_message">欢迎使用智能语音音乐播放器</string>
    <style name="AppButton">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_weight">1</item>
        <item name="android:background">@drawable/button_background</item>
        <item name="android:textColor">@color/button_text_color</item>
        <item name="android:padding">10dp</item>
        <item name="android:elevation">3dp</item>
        <item name="android:textAllCaps">false</item>
    </style>
    <style name="BottomSheetDialogTheme" parent="Theme.MaterialComponents.DayNight.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/BottomSheetStyle</item>
    </style>
    <style name="BottomSheetStyle" parent="Widget.MaterialComponents.BottomSheet.Modal">
        <item name="android:background">@drawable/bg_bottom_sheet</item>
        <item name="android:elevation">8dp</item>
        <item name="behavior_peekHeight">512dp</item>
    </style>
    <style name="FullScreenTheme" parent="Theme.MaterialComponents.NoActionBar">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="textInputStyle">@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox</item>

        
        <item name="colorBackground">@color/theme_background</item>
        <item name="textColorPrimary">@color/text_primary</item>
        <item name="textColorSecondary">@color/text_secondary</item>
    </style>
    <style name="LyricTextStyle">
        <item name="android:textSize">16sp</item>
        <item name="android:lineSpacingExtra">10dp</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/lyric_normal_color</item>
    </style>
    <style name="PlayPauseButton" parent="PlayerControlButton">
        <item name="android:layout_width">56dp</item>
        <item name="android:layout_height">56dp</item>
        <item name="android:layout_marginStart">16dp</item>
        <item name="android:layout_marginEnd">16dp</item>
    </style>
    <style name="PlayerControlButton">
        <item name="android:layout_width">48dp</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:padding">8dp</item>
        <item name="android:background">?attr/selectableItemBackgroundBorderless</item>
        <item name="android:tint">@color/primary_color</item>
    </style>
    <style name="SearchEditText">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_weight">1</item>
        <item name="android:background">@drawable/search_background</item>
        <item name="android:padding">12dp</item>
        <item name="android:textColorHint">@color/text_secondary_color</item>
        <item name="android:textColor">@color/text_primary_color</item>
        <item name="android:elevation">2dp</item>
    </style>
    <style name="SplashTheme" parent="Theme.AppCompat.NoActionBar">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
    </style>
    <style name="Theme.AIMusicPlayer" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor" ns1:targetApi="21">?attr/colorPrimaryVariant</item>
        
        <item name="textInputStyle">@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox</item>

        
        <item name="colorBackground">@color/theme_background</item>
        <item name="textColorPrimary">@color/text_primary</item>
        <item name="textColorSecondary">@color/text_secondary</item>
    </style>
    <declare-styleable name="LottieLoadingView">
        
        <attr format="string" name="lottieAnimationAsset"/>
        
        <attr format="string" name="loadingMessage"/>
        
        <attr format="boolean" name="autoPlay"/>
        
        <attr format="boolean" name="loop"/>
    </declare-styleable>
</resources>