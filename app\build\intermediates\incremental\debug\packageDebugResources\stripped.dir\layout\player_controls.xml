<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#EFEFEF"
    android:elevation="8dp"
    android:orientation="vertical"
    android:padding="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/songNameTextView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@android:color/black"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/artistTextView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@android:color/darker_gray"
                android:textSize="14sp" />

        </LinearLayout>

        <ImageView
            android:id="@+id/toggleLyricButton"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_gravity="center_vertical"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:clickable="true"
            android:contentDescription="切换歌词"
            android:focusable="true"
            android:padding="8dp"
            android:src="@android:drawable/ic_menu_edit" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/currentTimeTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="00:00"
            android:textSize="12sp" />

        <SeekBar
            android:id="@+id/seekBar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/totalTimeTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="00:00"
            android:textSize="12sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:gravity="center"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/previousButton"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:clickable="true"
            android:contentDescription="上一首"
            android:focusable="true"
            android:padding="8dp"
            android:src="@android:drawable/ic_media_previous" />

        <ImageView
            android:id="@+id/playPauseButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:clickable="true"
            android:contentDescription="播放/暂停"
            android:focusable="true"
            android:padding="8dp"
            android:src="@android:drawable/ic_media_play" />

        <ImageView
            android:id="@+id/nextButton"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:clickable="true"
            android:contentDescription="下一首"
            android:focusable="true"
            android:padding="8dp"
            android:src="@android:drawable/ic_media_next" />
    </LinearLayout>
</LinearLayout> 