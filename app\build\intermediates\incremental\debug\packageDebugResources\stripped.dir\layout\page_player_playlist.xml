<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!-- 播放列表标题栏 -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <TextView
            android:id="@+id/text_playlist_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="播放列表"
            android:textColor="@color/text_light"
            android:textSize="18sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/text_playlist_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toEndOf="@id/text_playlist_title"
            android:layout_marginStart="8dp"
            android:textColor="@color/color_gray_300"
            android:textSize="16sp"
            android:text="(0首)" />
    </RelativeLayout>

    <!-- 播放列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view_playlist"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="8dp"
        android:clipToPadding="false" />
</LinearLayout>
