<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000022"
    tools:context=".ui.driving.DrivingModeFragment">

    <!-- 顶部时间日期区域 -->
    <LinearLayout
        android:id="@+id/header_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="20dp">

        <TextView
            android:id="@+id/time_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="12:34"
            android:textColor="#FFFFFF"
            android:textSize="60sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/date_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="6月15日 周四"
            android:textColor="#AAAAAA"
            android:textSize="22sp" />
    </LinearLayout>

    <!-- 播放器区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/header_container"
        android:layout_marginTop="20dp"
        android:orientation="horizontal"
        android:gravity="center">

        <!-- 专辑封面区域 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="20dp">

            <FrameLayout
                android:layout_width="250dp"
                android:layout_height="250dp"
                android:layout_marginBottom="20dp">

                <ImageView
                    android:id="@+id/album_art"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/gradient_blue_purple"
                    android:scaleType="centerCrop" />

                <ProgressBar
                    android:id="@+id/loading_progress"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:layout_gravity="center"
                    android:visibility="gone" />
            </FrameLayout>

            <TextView
                android:id="@+id/song_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="歌曲名称"
                android:textColor="#FFFFFF"
                android:textSize="28sp"
                android:textStyle="bold"
                android:layout_marginBottom="5dp"
                android:ellipsize="end"
                android:gravity="center"
                android:maxLines="1" />

            <TextView
                android:id="@+id/artist_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="歌手名"
                android:textColor="#CCCCCC"
                android:textSize="20sp"
                android:layout_marginBottom="30dp" />
        </LinearLayout>

        <!-- 控制区域 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="20dp">

            <!-- 进度条 -->
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="30dp">

                <SeekBar
                    android:id="@+id/seek_bar"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:progressTint="#3498db"
                    android:thumbTint="#3498db" />

                <TextView
                    android:id="@+id/current_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/seek_bar"
                    android:text="0:00"
                    android:textColor="#AAAAAA"
                    android:textSize="18sp" />

                <TextView
                    android:id="@+id/total_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/seek_bar"
                    android:layout_alignParentEnd="true"
                    android:text="0:00"
                    android:textColor="#AAAAAA"
                    android:textSize="18sp" />
            </RelativeLayout>

            <!-- 播放控制按钮 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center"
                android:layout_marginBottom="40dp">

                <ImageButton
                    android:id="@+id/previous_button"
                    android:layout_width="70dp"
                    android:layout_height="70dp"
                    android:src="@drawable/ic_previous"
                    android:background="@android:color/transparent"
                    android:scaleType="fitCenter"
                    android:padding="15dp"
                    android:tint="#FFFFFF"
                    android:layout_marginEnd="30dp" />

                <ImageButton
                    android:id="@+id/play_pause_button"
                    android:layout_width="100dp"
                    android:layout_height="100dp"
                    android:src="@drawable/ic_play"
                    android:background="@android:color/transparent"
                    android:scaleType="fitCenter"
                    android:padding="15dp"
                    android:tint="#3498db"
                    android:layout_marginEnd="30dp" />

                <ImageButton
                    android:id="@+id/next_button"
                    android:layout_width="70dp"
                    android:layout_height="70dp"
                    android:src="@drawable/ic_next"
                    android:background="@android:color/transparent"
                    android:scaleType="fitCenter"
                    android:padding="15dp"
                    android:tint="#FFFFFF" />
            </LinearLayout>

            <!-- 快捷操作区域 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center">

                <LinearLayout
                    android:id="@+id/voice_control_container"
                    android:layout_width="160dp"
                    android:layout_height="160dp"
                    android:background="#1A3498DB"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp"
                    android:layout_margin="10dp">

                    <ImageButton
                        android:id="@+id/voice_button"
                        android:layout_width="70dp"
                        android:layout_height="70dp"
                        android:src="@android:drawable/ic_btn_speak_now"
                        android:background="@android:color/transparent"
                        android:scaleType="fitCenter"
                        android:tint="#3498db"
                        android:layout_marginBottom="10dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="语音控制"
                        android:textColor="#FFFFFF"
                        android:textSize="20sp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/exit_container"
                    android:layout_width="160dp"
                    android:layout_height="160dp"
                    android:background="#1AE74C3C"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp"
                    android:layout_margin="10dp">

                    <ImageButton
                        android:id="@+id/exit_button"
                        android:layout_width="70dp"
                        android:layout_height="70dp"
                        android:src="@android:drawable/ic_menu_close_clear_cancel"
                        android:background="@android:color/transparent"
                        android:scaleType="fitCenter"
                        android:tint="#E74C3C"
                        android:layout_marginBottom="10dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="退出驾驶模式"
                        android:textColor="#FFFFFF"
                        android:textSize="20sp" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</RelativeLayout>
