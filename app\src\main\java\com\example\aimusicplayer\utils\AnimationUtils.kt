package com.example.aimusicplayer.utils

import android.animation.*
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.OvershootInterpolator
import androidx.core.animation.doOnEnd

/**
 * 动画工具类
 * 提供各种常用的动画效果
 */
object AnimationUtils {

    /**
     * 淡入动画
     * @param view 目标视图
     * @param duration 动画时长
     * @param onEnd 动画结束回调
     */
    fun fadeIn(view: View, duration: Long = 300, onEnd: (() -> Unit)? = null) {
        view.alpha = 0f
        view.visibility = View.VISIBLE
        view.animate()
            .alpha(1f)
            .setDuration(duration)
            .setInterpolator(AccelerateDecelerateInterpolator())
            .withEndAction { onEnd?.invoke() }
            .start()
    }

    /**
     * 淡出动画
     * @param view 目标视图
     * @param duration 动画时长
     * @param hideAfter 动画结束后是否隐藏视图
     * @param onEnd 动画结束回调
     */
    fun fadeOut(view: View, duration: Long = 300, hideAfter: Boolean = true, onEnd: (() -> Unit)? = null) {
        view.animate()
            .alpha(0f)
            .setDuration(duration)
            .setInterpolator(AccelerateDecelerateInterpolator())
            .withEndAction {
                if (hideAfter) view.visibility = View.GONE
                onEnd?.invoke()
            }
            .start()
    }

    /**
     * 缩放动画
     * @param view 目标视图
     * @param fromScale 起始缩放比例
     * @param toScale 结束缩放比例
     * @param duration 动画时长
     * @param onEnd 动画结束回调
     */
    fun scale(view: View, fromScale: Float, toScale: Float, duration: Long = 300, onEnd: (() -> Unit)? = null) {
        view.scaleX = fromScale
        view.scaleY = fromScale
        view.animate()
            .scaleX(toScale)
            .scaleY(toScale)
            .setDuration(duration)
            .setInterpolator(OvershootInterpolator())
            .withEndAction { onEnd?.invoke() }
            .start()
    }

    /**
     * 按钮点击反馈动画
     * @param view 目标视图
     * @param scale 缩放比例
     * @param duration 动画时长
     */
    fun buttonClickFeedback(view: View, scale: Float = 0.95f, duration: Long = 100) {
        val scaleDown = ObjectAnimator.ofPropertyValuesHolder(
            view,
            PropertyValuesHolder.ofFloat("scaleX", scale),
            PropertyValuesHolder.ofFloat("scaleY", scale)
        ).apply {
            this.duration = duration
            interpolator = AccelerateDecelerateInterpolator()
        }

        val scaleUp = ObjectAnimator.ofPropertyValuesHolder(
            view,
            PropertyValuesHolder.ofFloat("scaleX", 1f),
            PropertyValuesHolder.ofFloat("scaleY", 1f)
        ).apply {
            this.duration = duration
            interpolator = AccelerateDecelerateInterpolator()
        }

        AnimatorSet().apply {
            playSequentially(scaleDown, scaleUp)
            start()
        }
    }

    /**
     * 滑入动画
     * @param view 目标视图
     * @param fromX 起始X位置偏移
     * @param duration 动画时长
     * @param onEnd 动画结束回调
     */
    fun slideIn(view: View, fromX: Float = 300f, duration: Long = 300, onEnd: (() -> Unit)? = null) {
        view.translationX = fromX
        view.alpha = 0f
        view.visibility = View.VISIBLE
        view.animate()
            .translationX(0f)
            .alpha(1f)
            .setDuration(duration)
            .setInterpolator(AccelerateDecelerateInterpolator())
            .withEndAction { onEnd?.invoke() }
            .start()
    }

    /**
     * 滑出动画
     * @param view 目标视图
     * @param toX 结束X位置偏移
     * @param duration 动画时长
     * @param hideAfter 动画结束后是否隐藏视图
     * @param onEnd 动画结束回调
     */
    fun slideOut(view: View, toX: Float = -300f, duration: Long = 300, hideAfter: Boolean = true, onEnd: (() -> Unit)? = null) {
        view.animate()
            .translationX(toX)
            .alpha(0f)
            .setDuration(duration)
            .setInterpolator(AccelerateDecelerateInterpolator())
            .withEndAction {
                if (hideAfter) view.visibility = View.GONE
                onEnd?.invoke()
            }
            .start()
    }

    /**
     * 旋转动画
     * @param view 目标视图
     * @param fromRotation 起始旋转角度
     * @param toRotation 结束旋转角度
     * @param duration 动画时长
     * @param onEnd 动画结束回调
     */
    fun rotate(view: View, fromRotation: Float, toRotation: Float, duration: Long = 300, onEnd: (() -> Unit)? = null) {
        view.rotation = fromRotation
        view.animate()
            .rotation(toRotation)
            .setDuration(duration)
            .setInterpolator(AccelerateDecelerateInterpolator())
            .withEndAction { onEnd?.invoke() }
            .start()
    }

    /**
     * 弹跳动画
     * @param view 目标视图
     * @param amplitude 弹跳幅度
     * @param duration 动画时长
     */
    fun bounce(view: View, amplitude: Float = 0.1f, duration: Long = 600) {
        val bounceAnimator = ObjectAnimator.ofFloat(view, "scaleY", 1f, 1f + amplitude, 1f)
        bounceAnimator.duration = duration
        bounceAnimator.interpolator = OvershootInterpolator()
        bounceAnimator.start()
    }

    /**
     * 心跳动画
     * @param view 目标视图
     * @param scale 缩放比例
     * @param duration 动画时长
     */
    fun heartbeat(view: View, scale: Float = 1.2f, duration: Long = 300) {
        val scaleUp = ObjectAnimator.ofPropertyValuesHolder(
            view,
            PropertyValuesHolder.ofFloat("scaleX", scale),
            PropertyValuesHolder.ofFloat("scaleY", scale)
        ).apply {
            this.duration = duration / 2
            interpolator = AccelerateDecelerateInterpolator()
        }

        val scaleDown = ObjectAnimator.ofPropertyValuesHolder(
            view,
            PropertyValuesHolder.ofFloat("scaleX", 1f),
            PropertyValuesHolder.ofFloat("scaleY", 1f)
        ).apply {
            this.duration = duration / 2
            interpolator = AccelerateDecelerateInterpolator()
        }

        AnimatorSet().apply {
            playSequentially(scaleUp, scaleDown)
            start()
        }
    }

    /**
     * 摇摆动画
     * @param view 目标视图
     * @param amplitude 摇摆幅度
     * @param duration 动画时长
     */
    fun shake(view: View, amplitude: Float = 10f, duration: Long = 500) {
        val shake = ObjectAnimator.ofFloat(view, "translationX", 0f, amplitude, -amplitude, amplitude, -amplitude, 0f)
        shake.duration = duration
        shake.start()
    }
}
